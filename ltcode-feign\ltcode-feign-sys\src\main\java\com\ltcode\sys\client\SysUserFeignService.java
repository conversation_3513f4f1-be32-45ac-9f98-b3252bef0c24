package com.ltcode.sys.client;


import com.ltcode.common.base.constant.ServiceNameConstants;
import com.ltcode.common.base.web.domain.ApiResponse;
import com.ltcode.sys.dto.SysUserDTO;
import com.ltcode.sys.factory.SysUserFeignFallBackFactory;
import com.ltcode.sys.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 用户服务
 * 
 */
@FeignClient(contextId = "sysUserFeignService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = SysUserFeignFallBackFactory.class)
public interface SysUserFeignService
{

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    @PostMapping("/user/register")
    ApiResponse<Boolean> registerUserInfo(@RequestBody SysUserDTO sysUser);

    /**
     * 获取用户信息
     *
     * @return 结果
     */
    @GetMapping("/user/getInfo")
    ApiResponse<LoginUser> getUserInfo();

}
