package com.ltcode.sys;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(exclude =  {DataSourceAutoConfiguration.class})
@MapperScan("com.ltcode.*.mapper")
public class LtCodeSysApplication {
    public static void main(String[] args) {
        {
            SpringApplication.run(LtCodeSysApplication.class, args);
            System.out.println("(✧ω✧)ﾉﾞ  系统业务服务启动成功   ヽ(•̀ω•́ )ゝ\n" +
                    " .----------------.       _____       \n" +
                    " |  LTCODE  _   \\     / ____|      \n" +
                    " | ( '_'  ) (_)  |    | (___   ___  \n" +
                    " | |< _ >|  _   /     \\___ \\ / _ \\ \n" +
                    " | |(_'_)|| | | |     ____) |  __/ \n" +
                    " |  \\___/ |_| |_|    |_____/ \\___| \n" +
                    " |                |                 \n" +
                    " '----------------'                 ");
        }
    }
}