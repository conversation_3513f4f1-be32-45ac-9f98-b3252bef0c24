package com.ltcode;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(exclude =  {DataSourceAutoConfiguration.class})
public class LtCodeGateWayApplication {
    public static void main(String[] args) {
        SpringApplication.run(LtCodeGateWayApplication.class, args);
        System.out.println("(✧ω✧)ﾉﾞ  网关服务启动成功   ヽ(•̀ω•́ )ゝ\n" +
                " .----------------.       _____       \n" +
                " |  LTCODE  _   \\     / ____|      \n" +
                " | ( '_'  ) (_)  |    | (___   ___  \n" +
                " | |< _ >|  _   /     \\___ \\ / _ \\ \n" +
                " | |(_'_)|| | | |     ____) |  __/ \n" +
                " |  \\___/ |_| |_|    |_____/ \\___| \n" +
                " |                |                 \n" +
                " '----------------'                 ");
    }
}