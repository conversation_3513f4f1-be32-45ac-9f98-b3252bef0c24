<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ltcode</groupId>
        <artifactId>ltcode-cloud</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>ltcode-feign</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>ltcode-feign-sys</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ltcode</groupId>
            <artifactId>ltcode-common-base</artifactId>
        </dependency>
    </dependencies>
</project>