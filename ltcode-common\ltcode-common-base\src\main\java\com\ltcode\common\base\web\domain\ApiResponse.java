package com.ltcode.common.base.web.domain;


import com.ltcode.common.base.constant.Constants;

import java.io.Serializable;

/**
 * 响应信息主体
 *
 */
public class ApiResponse<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 成功 */
    public static final int SUCCESS = Constants.SUCCESS;

    /** 失败 */
    public static final int FAIL = Constants.FAIL;

    private int code;

    private String msg;

    private T data;

    public static <T> ApiResponse<T> ok()
    {
        return restResult(null, SUCCESS, null);
    }

    public static <T> ApiResponse<T> ok(T data)
    {
        return restResult(data, SUCCESS, null);
    }

    public static <T> ApiResponse<T> ok(T data, String msg)
    {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> ApiResponse<T> fail()
    {
        return restResult(null, FAIL, null);
    }

    public static <T> ApiResponse<T> fail(String msg)
    {
        return restResult(null, FAIL, msg);
    }

    public static <T> ApiResponse<T> fail(T data)
    {
        return restResult(data, FAIL, null);
    }

    public static <T> ApiResponse<T> fail(T data, String msg)
    {
        return restResult(data, FAIL, msg);
    }

    public static <T> ApiResponse<T> fail(int code, String msg)
    {
        return restResult(null, code, msg);
    }

    private static <T> ApiResponse<T> restResult(T data, int code, String msg)
    {
        ApiResponse<T> apiResult = new ApiResponse<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        return apiResult;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public T getData()
    {
        return data;
    }

    public void setData(T data)
    {
        this.data = data;
    }

    public static <T> Boolean isError(ApiResponse<T> ret)
    {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(ApiResponse<T> ret)
    {
        return ApiResponse.SUCCESS == ret.getCode();
    }
}
