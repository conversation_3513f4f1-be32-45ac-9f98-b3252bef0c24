package com.ltcode.auth.application.controller;


import com.ltcode.auth.application.request.LoginRequest;
import com.ltcode.auth.application.request.RegisterRequest;
import com.ltcode.auth.application.service.SysLoginService;
import com.ltcode.common.base.web.domain.ApiResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @Author： ltcode
 * @Create by： 2025/7/16 0016 21:51
 * @ClassName: AuthController
 * @Describe：
 */
@RestController
@AllArgsConstructor
public class AuthController {


    private final SysLoginService sysLoginService;


    @PostMapping("login")
    public ApiResponse<?> login(@RequestBody LoginRequest request)
    {
        // 用户登录
       Map<String,Object> resultMap= sysLoginService.login(request);
        // 获取登录token
        return ApiResponse.ok(resultMap);
    }

    @DeleteMapping("logout")
    public ApiResponse<?> logout(HttpServletRequest request)
    {
        sysLoginService.logout(request);
        return ApiResponse.ok();
    }

    @PostMapping("refresh")
    public ApiResponse<?> refresh(HttpServletRequest request)
    {
        sysLoginService.refresh(request);

        return ApiResponse.ok();
    }

    @PostMapping("register")
    public ApiResponse<?> register(@RequestBody RegisterRequest registerRequest)
    {
        // 用户注册
        sysLoginService.register(registerRequest);
        return ApiResponse.ok();
    }
}
