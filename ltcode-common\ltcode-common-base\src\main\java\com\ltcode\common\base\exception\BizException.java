package com.ltcode.common.base.exception;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Data
public final class BizException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     *
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public BizException()
    {
    }

    public BizException(String message)
    {
        this.message = message;
    }

    public BizException(String message, Integer code)
    {
        this.message = message;
        this.code = code;
    }

    public BizException(String message, Object ...args)
    {
        this.message = StrUtil.format(message, args);;
        this.code = 500;
    }

}