<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e90c9046-d46c-465c-a260-21aca95142d1" name="Changes" comment="ddd">
      <change afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/adapter/controller/UserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-feign/ltcode-feign-sys/src/main/java/com/ltcode/sys/client/SysUserFeignService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-feign/ltcode-feign-sys/src/main/java/com/ltcode/sys/client/SysUserFeignService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/application/service/UserApplicationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/application/service/UserApplicationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysDept.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysDept.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysDictData.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysDictData.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysDictType.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysDictType.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysLogininfor.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysLogininfor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysMenu.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysMenu.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysNotice.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysNotice.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysOperLog.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysOperLog.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysPost.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysPost.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysRole.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysRole.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysRoleDept.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysRoleDept.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysRoleMenu.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/model/SysRoleMenu.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/repository/impl/SysLogininforRepositoryImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/repository/impl/SysLogininforRepositoryImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/repository/impl/SysMenuRepositoryImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/repository/impl/SysMenuRepositoryImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/repository/impl/SysPostRepositoryImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/domain/repository/impl/SysPostRepositoryImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysConfigMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysConfigMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysDeptMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysDeptMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysDictDataMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysDictDataMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysDictTypeMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysDictTypeMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysLogininforMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysLogininforMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysMenuMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysMenuMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysNoticeMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysNoticeMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysOperLogMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysOperLogMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysPostMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysPostMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysRoleDeptMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysRoleDeptMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysRoleMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysRoleMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysRoleMenuMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/SysRoleMenuMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysConfigMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysConfigMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysDeptMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysDeptMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysDictDataMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysDictDataMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysDictTypeMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysDictTypeMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysLogininforMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysLogininforMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysMenuMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysMenuMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysNoticeMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysNoticeMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysOperLogMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysOperLogMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysPostMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysPostMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysRoleDeptMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysRoleDeptMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysRoleMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysRoleMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysRoleMenuMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/mapper/mappering/SysRoleMenuMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysConfigService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysConfigService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysDeptService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysDeptService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysDictDataService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysDictDataService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysDictTypeService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysDictTypeService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysLogininforService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysLogininforService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysMenuService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysMenuService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysNoticeService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysNoticeService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysOperLogService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysOperLogService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysPostService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysPostService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysRoleDeptService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysRoleDeptService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysRoleMenuService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysRoleMenuService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysRoleService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/SysRoleService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysConfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysConfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysDeptServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysDeptServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysDictDataServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysDictDataServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysDictTypeServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysDictTypeServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysLogininforServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysLogininforServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysMenuServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysMenuServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysNoticeServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysNoticeServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysOperLogServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysOperLogServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysPostServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysPostServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysRoleDeptServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysRoleDeptServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysRoleMenuServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysRoleMenuServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysRoleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-services/ltcode-service-sys/src/main/java/com/ltcode/sys/repository/service/impl/SysRoleServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://C:/Program Files/Java/jdk1.8.0_172/src.zip!/java/sql/DatabaseMetaData.java" root0="SKIP_INSPECTION" />
    <setting file="jar://C:/Program Files/Java/jdk1.8.0_172/src.zip!/java/sql/ResultSet.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../sjyt/work/workspace/repo/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38-sources.jar!/cn/hutool/core/io/FileUtil.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../sjyt/work/workspace/repo/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38-sources.jar!/cn/hutool/core/text/CharSequenceUtil.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../sjyt/work/workspace/repo/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar!/com/fasterxml/jackson/annotation/JsonInclude.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\owner\repo" />
        <option name="userSettingsFile" value="D:\owner\setting.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2ztuwrVB9JiOiVfoS6xYTEQ2kNZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JUnit.AuthApplicationTest.contextLoads.executor": "Run",
    "Maven.ltcode-cloud [clean].executor": "Run",
    "Maven.ltcode-cloud [compile].executor": "Run",
    "Maven.ltcode-common-log [clean].executor": "Run",
    "Maven.ltcode-common-log [compile].executor": "Run",
    "Maven.ltcode-common-log [deploy].executor": "Run",
    "Maven.ltcode-common-log [install].executor": "Run",
    "Maven.ltcode-common-log [package].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.LtCodeAuthApplication (1).executor": "Debug",
    "Spring Boot.LtCodeAuthApplication .executor": "Debug",
    "Spring Boot.LtCodeAuthApplication.executor": "Debug",
    "Spring Boot.LtCodeFileApplication.executor": "Debug",
    "Spring Boot.LtCodeGateWayApplication.executor": "Debug",
    "Spring Boot.LtCodeGenApplication (1).executor": "Debug",
    "Spring Boot.LtCodeGenApplication.executor": "Debug",
    "Spring Boot.LtCodeMessageApplication.executor": "Debug",
    "Spring Boot.LtCodeSysApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/owner/repo/com/an",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.36091954",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\owner\space\ltcode\ltcode-cloud\ltcode-common\ltcode-common-log\src\main" />
      <recent name="D:\owner\space\ltcode\ltcode-cloud\ltcode-common\ltcode-common-log\src\main\java\com.ltcode.log\annotation" />
      <recent name="D:\owner\space\ltcode\ltcode-cloud\ltcode-common\ltcode-common-log\src\main\java\com.ltcode.log" />
      <recent name="D:\owner\space\ltcode\ltcode-cloud\ltcode-common\ltcode-common-base\src\main" />
      <recent name="D:\owner\space\ltcode\ltcode-cloud\ltcode-common\ltcode-common-base\src\main\java\com\ltcode\common\base\exception" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\owner\space\ltcode\ltcode-cloud\ltcode-feign\ltcode-feign-sys\src\main\java\com\ltcode\sys\dto" />
      <recent name="D:\owner\space\ltcode\ltcode-cloud\ltcode-feign\ltcode-feign-sys\src\main\java\com\ltcode\sys" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.ltcode.common.base.utils" />
      <recent name="com.ltcode.gen.modular.basic.enums" />
      <recent name="com.ltcode.security.utils" />
      <recent name="com.ltcode.sys.controller" />
      <recent name="com.ltcode.sys.domain" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="JUnit.AuthApplicationTest.contextLoads">
    <configuration name="Unnamed" type="Application" factoryName="Application" nameIsGenerated="true">
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AuthApplicationTest.contextLoads" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ltcode-auth" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ltcode.auth.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ltcode.auth" />
      <option name="MAIN_CLASS_NAME" value="com.ltcode.auth.AuthApplicationTest" />
      <option name="METHOD_NAME" value="contextLoads" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtCodeAuthApplication " type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="ltcode-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.auth.LtCodeAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtCodeAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ltcode-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.LtCodeAuthApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ltcode.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtCodeFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ltcode-service-file" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.file.LtCodeFileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtCodeGateWayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ltcode-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.LtCodeGateWayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtCodeGenApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ltcode-service-gen" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.LtCodeGenApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ltcode.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtCodeGenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ltcode-service-gen" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.gen.LtCodeGenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtCodeMessageApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ltcode-service-message" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.message.LtCodeMessageApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtCodeSysApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ltcode-service-sys" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.sys.LtCodeSysApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.AuthApplicationTest.contextLoads" />
        <item itemvalue="Spring Boot.LtCodeGenApplication (1)" />
        <item itemvalue="Spring Boot.LtCodeAuthApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="init" />
    <MESSAGE value="sql" />
    <MESSAGE value="基础工具" />
    <MESSAGE value="验证码" />
    <MESSAGE value="sys-业务" />
    <MESSAGE value="代码生成" />
    <MESSAGE value="xx" />
    <MESSAGE value="ddd" />
    <option name="LAST_COMMIT_MESSAGE" value="ddd" />
    <option name="NON_MODAL_COMMIT_POSTPONE_SLOW_CHECKS" value="false" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>