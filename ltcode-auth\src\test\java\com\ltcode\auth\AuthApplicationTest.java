package com.ltcode.auth;

import com.an.code.starter.annotation.BizLog;
import com.ltcode.auth.application.service.ValidateCodeService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@SpringBootTest
public class AuthApplicationTest {
    @Resource
    private ValidateCodeService validateCodeService;
    @Test
    void contextLoads() {
        CompletableFuture<Void> check = CompletableFuture.runAsync(() -> {
            validateCodeService.check();
        });
        CompletableFuture<Void> check001 = CompletableFuture.runAsync(() -> {
            validateCodeService.check();
        });
        CompletableFuture.allOf(check, check001).join();
        System.out.println("结束");

    }
}
