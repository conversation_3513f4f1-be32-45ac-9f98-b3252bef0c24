package com.ltcode.auth.application.service;

import com.ltcode.auth.application.request.LoginRequest;
import com.ltcode.auth.application.request.RegisterRequest;
import com.ltcode.auth.utils.SecurityUtils;
import com.ltcode.common.base.web.domain.ApiResponse;
import com.ltcode.common.base.enums.UserStatus;
import com.ltcode.common.base.exception.BizException;
import com.ltcode.sys.client.SysUserFeignService;
import com.ltcode.sys.domain.SysUserDomain;
import com.ltcode.sys.dto.SysUserDTO;
import com.ltcode.sys.model.LoginUser;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SysLoginService
{


    private final ValidateCodeService validateCodeService;

    private final SysUserFeignService sysUserFeignService;

    private final TokenService tokenService;





    /**
     * 登录
     */
    public Map<String,Object> login(LoginRequest request)
    {
        //校验验证码
        validateCodeService.checkCaptcha(request.getCode(), request.getUuid());

        String username = request.getUsername();
        // 查询用户信息
        ApiResponse<LoginUser> userResult = sysUserFeignService.getUserInfo();

        if (ApiResponse.FAIL == userResult.getCode())
        {
            throw new BizException(userResult.getMsg());
        }
        LoginUser userInfo = userResult.getData();
        SysUserDomain user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            throw new BizException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            throw new BizException("对不起，您的账号：" + username + " 已停用");
        }
        //创建token
        return tokenService.createToken(userInfo);
    }




    /**
     * 注册
     */
    public void register(RegisterRequest request)
    {
        validateCodeService.checkCaptcha(request.getCode(), request.getUuid());
        // 注册用户信息
        SysUserDTO sysUser = new SysUserDTO();
        sysUser.setUserName(request.getUsername());
        sysUser.setNickName(request.getUsername());
        sysUser.setPassword(SecurityUtils.encryptPassword(request.getPassword()));
        ApiResponse<?> registerResult = sysUserFeignService.registerUserInfo(sysUser);

        if (ApiResponse.FAIL == registerResult.getCode())
        {
            throw new BizException(registerResult.getMsg());
        }
    }
    /***
     * 退出登录
     */
    public void logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        tokenService.delLoginUser(token);
    }

    /**
     * 刷新令牌有效期
     */
    public void refresh(HttpServletRequest request) {
//        LoginUser loginUser = tokenService.getLoginUser(request);
//        if (ObjectUtil.isNotNull(loginUser))
//        {
//            // 刷新令牌有效期
//            tokenService.refreshToken(loginUser);
//        }
    }
}
