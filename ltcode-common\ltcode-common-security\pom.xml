<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ltcode</groupId>
        <artifactId>ltcode-common</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>ltcode-common-security</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- RuoYi Api System -->
        <dependency>
            <groupId>com.ltcode</groupId>
            <artifactId>ltcode-feign-sys</artifactId>
        </dependency>

        <!-- RuoYi Common Redis-->
        <dependency>
            <groupId>com.ltcode</groupId>
            <artifactId>ltcode-common-cache</artifactId>
        </dependency>
    </dependencies>
</project>