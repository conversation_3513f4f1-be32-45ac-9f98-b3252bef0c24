package com.ltcode.auth.application.controller;

import com.ltcode.auth.application.service.ValidateCodeService;
import com.ltcode.common.base.web.domain.ApiResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/captcha")
@AllArgsConstructor
public class CaptchaController {
    private final ValidateCodeService validateCodeService;

    /**
     * 验证码
     * @return
     */
    @GetMapping("/code")
    public ApiResponse code() {
        return validateCodeService.createCaptcha();
    }


}
