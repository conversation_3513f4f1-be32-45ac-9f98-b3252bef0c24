package com.ltcode.auth.application.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author： ltcode
 * @Create by： 2025/7/16 0016 22:02
 * @ClassName: LoginRequest
 * @Describe：
 */
@Data
public class LoginRequest {
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 用户密码
     */
    @NotBlank(message = "用户密码不能为空")
    private String password;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String code;
    /**
     * 验证码唯一标识
     */
    private String uuid;

}
