spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password:
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: ltcode-auth
          uri: lb://ltcode-auth
          predicates:
            - Path=/auth/**
          filters:
            # 去掉前缀 auth 只保留后续请求地址直接转发
            - StripPrefix=1
        # 代码生成
        - id: ltcode-service-gen
          uri: lb://ltcode-service-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: ltcode-service-sys
          uri: lb://ltcode-service-sys
          predicates:
            - Path=/sys/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: ltcode-service-file
          uri: lb://ltcode-service-file
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1

# 安全配置
security:
  # 不校验白名单
  ignore:
    whites:
      - /auth/logout
      - /auth/login
      - /auth/register
      - /auth/captcha/code
      - /*/v2/api-docs
      - /*/v3/api-docs
      - /csrf

