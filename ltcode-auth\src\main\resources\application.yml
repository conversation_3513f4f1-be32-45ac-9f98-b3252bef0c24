# Tomcat
server:
  port: 10001

# Spring
spring:
  application:
    # 应用名称
    name: ltcode-auth
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yml
        import-check:
          enabled: false
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

  redis:
    host: 127.0.0.1
    port: 6379
    password:
# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: math