# Tomcat
server:
  port: 10002

# Spring
spring:
  application:
    # 应用名称
    name: ltcode-gateway
  profiles:
    # 环境配置
    active: local
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        import-check:
          enabled: false
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 127.0.0.1:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: 127.0.0.1:8848
            dataId: sentinel-ltcode-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
      enabled: false
