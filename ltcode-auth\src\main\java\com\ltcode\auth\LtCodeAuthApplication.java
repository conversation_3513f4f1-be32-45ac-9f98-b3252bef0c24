package com.ltcode.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@SpringBootApplication(exclude =  {DataSourceAutoConfiguration.class})
@EnableFeignClients(basePackages = {"com.ltcode"})
public class LtCodeAuthApplication {
    public static void main(String[] args)
    {
        SpringApplication.run(LtCodeAuthApplication.class, args);
        System.out.println("(✧ω✧)ﾉﾞ  认证服务启动成功   ヽ(•̀ω•́ )ゝ\n" +
                " .----------------.       _____       \n" +
                " |  LTCODE  _   \\     / ____|      \n" +
                " | ( '_'  ) (_)  |    | (___   ___  \n" +
                " | |< _ >|  _   /     \\___ \\ / _ \\ \n" +
                " | |(_'_)|| | | |     ____) |  __/ \n" +
                " |  \\___/ |_| |_|    |_____/ \\___| \n" +
                " |                |                 \n" +
                " '----------------'                 ");
    }
}