package com.ltcode.common.base.exception.handler;

import cn.hutool.core.util.ObjectUtil;
import com.ltcode.common.base.exception.BizException;
import com.ltcode.common.base.web.domain.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler
{

    /**
     * 业务异常
     */
    @ExceptionHandler(BizException.class)
    public ApiResponse handleServiceException(BizException e, HttpServletRequest request)
    {
        log.error(e.getMessage(), e);
        Integer code = e.getCode();
        return ObjectUtil.isNotNull(code) ? ApiResponse.fail(code, e.getMessage()) : ApiResponse.fail(e.getMessage());
    }


}
