package com.ltcode.sys.factory;

import com.ltcode.common.base.web.domain.ApiResponse;
import com.ltcode.sys.client.SysUserFeignService;
import com.ltcode.sys.dto.SysUserDTO;
import com.ltcode.sys.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SysUserFeignFallBackFactory implements FallbackFactory<SysUserFeignService> {
    @Override
    public SysUserFeignService create(Throwable throwable) {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new SysUserFeignService() {
            @Override
            public ApiResponse<Boolean> registerUserInfo(SysUserDTO sysUser) {
                return ApiResponse.fail("注册用户失败");
            }

            @Override
            public ApiResponse<LoginUser> getUserInfo() {
                return ApiResponse.fail("获取用户信息失败");
            }
        };
    }
}
