package com.ltcode.auth.application.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.an.code.starter.annotation.BizLog;
import com.google.code.kaptcha.Producer;
import com.ltcode.auth.application.service.ValidateCodeService;
import com.ltcode.auth.config.CaptchaProperties;
import com.ltcode.cache.service.RedisService;
import com.ltcode.common.base.constant.CacheConstants;
import com.ltcode.common.base.exception.BizException;
import com.ltcode.common.base.web.domain.ApiResponse;
import com.ltcode.common.base.utils.sign.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.FastByteArrayOutputStream;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 验证码实现处理
 *
 */
@Service
public class ValidateCodeServiceImpl implements ValidateCodeService
{
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CaptchaProperties captchaProperties;

    /**
     * 生成验证码
     */
    @Override
    public ApiResponse createCaptcha()
    {

        Map<String, Object> map = MapUtil.createMap(HashMap.class);
        boolean captchaEnabled = captchaProperties.getEnabled();
        map.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled)
        {
            return ApiResponse.fail(map);
        }

        // 保存验证码信息
        String uuid = IdUtil.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        String captchaType = captchaProperties.getType();
        // 生成验证码
        if ("math".equals(captchaType))
        {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        }
        else if ("char".equals(captchaType))
        {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

//        redisService.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            return ApiResponse.fail(e.getMessage());
        }

        map.put("uuid", uuid);
        map.put("img", Base64.encode(os.toByteArray()));
        return ApiResponse.ok(map);
    }

    /**
     * 校验验证码
     */
    @Override
    public void checkCaptcha(String code, String uuid)
    {
        if (StrUtil.isEmpty(code))
        {
            throw new BizException("验证码不能为空");
        }
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + (StrUtil.isNotBlank(uuid)?uuid:"");
        String captcha = redisService.getCacheObject(verifyKey);
        if (captcha == null)
        {
            throw new BizException("验证码已失效");
        }
        redisService.deleteObject(verifyKey);
        if (!code.equalsIgnoreCase(captcha))
        {
            throw new BizException("验证码错误");
        }
    }

}
