package com.ltcode.message;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class LtCodeMessageApplication {
    public static void main(String[] args) {
        {
            SpringApplication.run(LtCodeMessageApplication.class, args);
            System.out.println("(✧ω✧)ﾉﾞ  消息服务启动成功   ヽ(•̀ω•́ )ゝ\n" +
                    " .----------------.       _____       \n" +
                    " |  LTCODE  _   \\     / ____|      \n" +
                    " | ( '_'  ) (_)  |    | (___   ___  \n" +
                    " | |< _ >|  _   /     \\___ \\ / _ \\ \n" +
                    " | |(_'_)|| | | |     ____) |  __/ \n" +
                    " |  \\___/ |_| |_|    |_____/ \\___| \n" +
                    " |                |                 \n" +
                    " '----------------'                 ");
        }
    }
}